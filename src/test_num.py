import re

number_map = {
        '0': '零', '1': '一', '2': '二', '3': '三', '4': '四',
        '5': '五', '6': '六', '7': '七', '8': '八', '9': '九'
    }
def convert_digit_section(digit_str):
    """转换四位以内的数字段"""
    # number_map = {
    #     '0': '零', '1': '一', '2': '二', '3': '三', '4': '四',
    #     '5': '五', '6': '六', '7': '七', '8': '八', '9': '九'
    # }
    unit_map = ['', '十', '百', '千']
    
    result = ''
    digit_str = digit_str.zfill(4)  # 补零到4位
    has_previous_digit = False  # 用于处理零的显示
    
    for i, d in enumerate(digit_str):
        if d == '0':
            if has_previous_digit:  # 只有在前面有非零数字时才显示零
                if result[-1] != '零':  # 避免重复的零
                    result += '零'
            has_previous_digit = False
        else:
            result += number_map[d] + unit_map[3-i]
            # result += number_map[d]
            has_previous_digit = True
    
    # 清理结果
    result = result.rstrip('零')  # 移除末尾的零
    
    # 处理特殊情况
    if result.startswith('一十'):
        result = result[1:]
        
    return result or '零'

def convert_number(match):
    """转换匹配到的完整数字"""
    num = match.group(1)
    word = match.group(2)
    print(f'{num} and {word}')
    
    # 如果后面有年字，只读数字，不处理成百千万
    if word == '年':
        result = ''
        for i, d in enumerate(num):
            result += number_map[d]
        result += '年'
        return result
    
    # 处理小于100的数字的特殊情况
    if len(num) <= 2:
        if num == '10':
            return '十'+word
        elif num.startswith('0'):
            return convert_digit_section(num.lstrip('0'))+word
        elif len(num) == 2 and num.startswith('1'):
            return '十' + (convert_digit_section(num[1]) if num[1] != '0' else '')+word
    
    # 对于大数字，按4位分段处理
    units = ['', '万', '亿']
    segments = []
    while num:
        seg = num[-4:] if len(num) >= 4 else num
        converted = convert_digit_section(seg)
        if converted != '零':  # 只添加非零的段
            segments.append(converted)
        num = num[:-4]
    
    # 添加单位并反转
    result = ''
    for i, seg in enumerate(segments):
        if seg != '零':  # 只给非零段加单位
            result = seg + units[i] + result
    if word:
        result += word
    return result or '零'

text = '开放时间为8，60， 8:30—22:00'
print( re.sub(r'(\d+)(?:([\u4e00-\u9fa5]))?', lambda m: convert_number(m), text))