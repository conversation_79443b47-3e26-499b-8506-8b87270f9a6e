from tomlkit import parse, dumps
# 加载TOML文件
config_path = "f5_tts/infer/examples/basic/basic.toml"#"../config/settings.toml"
# 1. 读取 TOML 文件
with open(config_path, "r") as f:
    config_str = f.read()
    config = parse(config_str)
print(config)

# config['gen_text'] = '各位来宾，大家好，我是讲解员AI之眼。二零二五年七月三日，nice，很高兴为大家讲解。现在我们已经进入了红军长征湘江战役纪念园的区域，这个纪念园建在湘江战役脚山铺阻击战的战场遗址上。我们行驶的这条公路是国道桂黄公路，它的左边是先锋岭，右边是米花山，先锋岭和米花山都是湘江战役的主战场，湘江就位于我们正前方3公里的地方。大家看路边这一排排青翠的刚竹，它们象征着红军战士刚强坚韧、百折不挠的品质。'
# config['output_file'] = 'infer_cli_test.wav'
config['test'] = 'test'
 
# 3. 将修改写回文件
with open(config_path, "a+") as f:
    f.write(dumps(config))