import asyncio
import aiohttp
import time
import statistics
from datetime import datetime

async def benchmark_concurrency(url, data, params,concurrency_level, duration=30):
    """测试指定并发级别下的性能"""
    successful_requests = 0
    failed_requests = 0
    request_times = []
    
    async def worker(session, worker_id):
        nonlocal successful_requests, failed_requests
        start_time = time.time()
        
        while time.time() - start_time < duration:
            req_start = time.time()
            try:
                async with session.post(url, data=data,params=params) as response:
                    await response.read()
                    req_time = time.time() - req_start
                    
                    if response.status == 200:
                        successful_requests += 1
                        request_times.append(req_time)
                    else:
                        failed_requests += 1
                        
            except Exception as e:
                failed_requests += 1
                print(f"Worker {worker_id} error: {e}")
    
    connector = aiohttp.TCPConnector(limit=concurrency_level*2)
    timeout = aiohttp.ClientTimeout(total=10)
    
    async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
        tasks = [worker(session, i) for i in range(concurrency_level)]
        await asyncio.gather(*tasks)
    
    total_requests = successful_requests + failed_requests
    qps = successful_requests / duration
    
    return {
        'concurrency': concurrency_level,
        'duration': duration,
        'total_requests': total_requests,
        'successful_requests': successful_requests,
        'failed_requests': failed_requests,
        'success_rate': (successful_requests/total_requests*100) if total_requests > 0 else 0,
        'qps': qps,
        'avg_response_time': statistics.mean(request_times) if request_times else 0,
        'p95_response_time': statistics.quantiles(request_times, n=20)[18] if len(request_times) >= 20 else 0
    }

async def main():
    url = "http://127.0.0.1:6000/tts"
    data = {'text': 'hello world! 大家好，人民网成立25周年，25年成立。'}
    # url = "http://127.0.0.1:8090/voice"
    # # DATA = {'text': 'hello world! 大家好，人民网成立25周年，25年成立。'}
    params = {
        "model_id": 0,
        "speaker_name": "yyz",
        "length": 1,
        "language": "ZH",
        "auto_split": 0,
        "sdp_ratio": 0.8,
        "noise": 0.6,
        "noisew": 0.8,
    }
    # data = {
    #     "text": '大家好，人民网成立25周年，25年成立。',  #hello world! 
    # }
    # 测试不同并发级别
    concurrency_levels = [1, 5, 10, 20, 50, 100]
    results = []
    
    print("开始FastAPI并发性能测试...")
    print("="*80)
    
    for level in concurrency_levels:
        print(f"测试并发级别: {level}")
        result = await benchmark_concurrency(url, data,params, level, duration=30)
        results.append(result)
        
        print(f"QPS: {result['qps']:.2f}, 成功率: {result['success_rate']:.1f}%, "
              f"平均响应时间: {result['avg_response_time']:.3f}s")
        print("-" * 40)
        
        # 间隔一段时间让服务器恢复
        await asyncio.sleep(5)
    
    # 输出汇总报告
    print("\n" + "="*80)
    print("性能测试汇总报告")
    print("="*80)
    print(f"{'并发数':<8} {'QPS':<10} {'成功率':<8} {'平均响应时间':<12} {'P95响应时间':<12}")
    print("-" * 60)
    
    for result in results:
        print(f"{result['concurrency']:<8} {result['qps']:<10.2f} "
              f"{result['success_rate']:<8.1f}% {result['avg_response_time']:<12.3f} "
              f"{result['p95_response_time']:<12.3f}")

if __name__ == "__main__":
    asyncio.run(main())