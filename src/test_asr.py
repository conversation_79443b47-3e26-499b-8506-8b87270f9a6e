import requests
import threading
import time
import statistics
from datetime import datetime
import subprocess

# 可配置项
ENABLE_FAILURE_LOGGING = True
TOTAL_REQUESTS = 10
URL = "http://127.0.0.1:8110/asr/asr_offline"


# 全局统计变量
fail_num = 0
success_num = 0
response_times = []
lock = threading.Lock()

def changeSampleRate(file_path, output_path):
    try:
        cmd = f"ffmpeg -i {file_path} -ac 1 -ar 16000 -sample_fmt s16 -y {output_path}"
        subprocess.run(cmd, shell=True)
    except:
        pass
changeSampleRate('audio.wav','audio_asr.wav')
test_audio = 'audio_asr.wav'
with open(test_audio, 'rb') as f:
    data = f.read()

def send_request():
    global fail_num, success_num
    start_time = time.time()
    
    try:
        response = requests.post(URL,  data=data)#params=params,, timeout=30
        elapsed_time = time.time() - start_time
        
        with lock:
            if response.status_code == 200:
                success_num += 1
                response_times.append(elapsed_time)
            else:
                fail_num += 1
                if ENABLE_FAILURE_LOGGING:
                    print(f"Request failed with status code {response.status_code}")
                    
    except Exception as e:
        elapsed_time = time.time() - start_time
        with lock:
            fail_num += 1
            if ENABLE_FAILURE_LOGGING:
                print(f"Request failed with exception: {e}")

if __name__ == "__main__":
    print(f"开始Flask接口并发测试: {TOTAL_REQUESTS} 个并发请求")
    print(f"目标URL: {URL}")
    print("-" * 50)
    
    start_time = time.time()
    
    threads = []
    for i in range(TOTAL_REQUESTS):
        t = threading.Thread(target=send_request)
        threads.append(t)
        t.start()

    for t in threads:
        t.join()
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 计算统计信息
    if response_times:
        avg_time = statistics.mean(response_times)
        median_time = statistics.median(response_times)
        min_time = min(response_times)
        max_time = max(response_times)
        p95_time = statistics.quantiles(response_times, n=20)[18] if len(response_times) >= 20 else max_time
    else:
        avg_time = median_time = min_time = max_time = p95_time = 0
    
    # 输出性能报告
    print("\n" + "="*60)
    print("Flask接口并发性能测试报告")
    print("="*60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"并发请求数: {TOTAL_REQUESTS}")
    print(f"总耗时: {total_time:.2f}秒")
    print(f"成功请求数: {success_num}")
    print(f"失败请求数: {fail_num}")
    print(f"成功率: {(success_num/(success_num+fail_num)*100):.1f}%")
    print(f"QPS (每秒请求数): {success_num/total_time:.2f}")
    print("-" * 40)
    print("响应时间统计:")
    print(f"  平均响应时间: {avg_time:.3f}秒")
    print(f"  中位数响应时间: {median_time:.3f}秒")
    print(f"  最小响应时间: {min_time:.3f}秒")
    print(f"  最大响应时间: {max_time:.3f}秒")
    print(f"  95%响应时间: {p95_time:.3f}秒")
    print("="*60)

# import requests
# import time
# import os

# test_audio = 'audio.wav'
# ave_time = 0
# inf_num = 0


# with open(test_audio, 'rb') as f:
#     data = f.read()
# try:
#     s = time.time()
#     res = requests.post("http://127.0.0.1:8110/asr/asr_offline", data=data)
#     #res = requests.post("http://127.0.0.1:6000/asr_binary", data=data)
#     e =  time.time()
#     print(res.json()['results'])
#     # print(f'inference time: {e-s}')
#     ave_time = (ave_time*inf_num + (e-s))/(inf_num+1)
#     print(f'average time: {ave_time}')
#     inf_num += 1
# except Exception as e:
#     print(e)




