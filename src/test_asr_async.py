import aiohttp
import asyncio
import time
import statistics
from datetime import datetime
import subprocess
# 配置项
TOTAL_REQUESTS = 100
URL = "http://127.0.0.1:8110/asr/asr_offline"

# params = {
#     "model_id": 0,
#     "speaker_name": "yyz",
#     "length": 1,
#     "language": "ZH",
#     "auto_split": False,
#     "sdp_ratio": 0.8,
#     "noise": 0.6,
#     "noisew": 0.8,
# }
# payload = {
#     "text": 'hello world! 大家好，人民网成立25周年，25年成立。',
# }
def changeSampleRate(file_path, output_path):
    try:
        cmd = f"ffmpeg -i {file_path} -ac 1 -ar 16000 -sample_fmt s16 -y {output_path}"
        subprocess.run(cmd, shell=True)
    except:
        pass
changeSampleRate('audio.wav','audio_asr.wav')
test_audio = 'audio_asr.wav'
with open(test_audio, 'rb') as f:
    data = f.read()
# 全局统计变量
success_num = 0
fail_num = 0
response_times = []
lock = asyncio.Lock()

async def send_request(session, request_id):
    global success_num, fail_num
    
    start_time = time.time()
    try:
        async with session.post(URL, data=data) as response: #params=params, 
            await response.read()
            elapsed_time = time.time() - start_time
            
            async with lock:
                if response.status == 200:
                    success_num += 1
                    response_times.append(elapsed_time)
                else:
                    fail_num += 1
                    print(f"Request {request_id} failed with status {response.status}")
                    
    except Exception as e:
        elapsed_time = time.time() - start_time
        async with lock:
            fail_num += 1
            print(f"Request {request_id} failed: {e}")

async def main():
    print(f"开始Flask接口异步并发测试: {TOTAL_REQUESTS} 个并发请求")
    print(f"目标URL: {URL}")
    print("-" * 50)
    
    connector = aiohttp.TCPConnector(limit=200, limit_per_host=100)
    timeout = aiohttp.ClientTimeout(total=30)
    
    async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
        tasks = [send_request(session, i) for i in range(TOTAL_REQUESTS)]
        await asyncio.gather(*tasks, return_exceptions=True)

if __name__ == "__main__":
    start_time = time.time()
    asyncio.run(main())
    end_time = time.time()
    
    total_time = end_time - start_time
    
    # 计算统计信息
    if response_times:
        avg_time = statistics.mean(response_times)
        median_time = statistics.median(response_times)
        min_time = min(response_times)
        max_time = max(response_times)
        p95_time = statistics.quantiles(response_times, n=20)[18] if len(response_times) >= 20 else max_time
    else:
        avg_time = median_time = min_time = max_time = p95_time = 0
    
    # 输出性能报告
    print("\n" + "="*60)
    print("Flask接口异步并发性能测试报告")
    print("="*60)
    print(f"并发请求数: {TOTAL_REQUESTS}")
    print(f"总耗时: {total_time:.2f}秒")
    print(f"成功请求数: {success_num}")
    print(f"失败请求数: {fail_num}")
    print(f"成功率: {(success_num/(success_num+fail_num)*100):.1f}%")
    print(f"QPS: {success_num/total_time:.2f}")
    print(f"平均响应时间: {avg_time:.3f}秒")
    print(f"95%响应时间: {p95_time:.3f}秒")
    print("="*60)