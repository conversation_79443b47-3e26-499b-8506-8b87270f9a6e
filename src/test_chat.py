import asyncio
import websockets
import json
import time
import base64
import os
import random
# 配置
WS_URL = "ws://127.0.0.1:9214/chat"
IMAGE_PATH = "/private/huqq/Projects/Navigation/location_mast3r_slam/logs/img87d/"

with open('/private/liulz/TTS/F5-TTS/src/tests/infer_cli_20250718_152229.wav', 'rb') as f:
    audio_base64 = base64.b64encode(f.read()).decode()

async def quick_test(concurrency=20, duration=1):
    """快速WebSocket并发测试"""
    successful = 0
    failed = 0
    times = []
    
    img_list = os.listdir(IMAGE_PATH)  # 只用1张图片
    selected_images = random.sample(img_list, 2)
    async def worker(worker_id):
        nonlocal successful, failed
        try:
            async with websockets.connect(WS_URL) as ws:
                start = time.time()
                while time.time() - start < duration:
                    for img_name in selected_images:
                        full_path = os.path.join(IMAGE_PATH, img_name)
                        
                        message = {
                            'id': f"test_{worker_id}_{int(time.time()*1000)}",
                            'msg': "query",
                            'audio': audio_base64,
                            'video': "",
                            'exhibition_id': 0,
                            'frame_time': round(time.time(), 2)
                        }
                        
                        try:
                            with open(full_path, 'rb') as f:
                                message['video'] = base64.b64encode(f.read()).decode()
                        except:
                            continue
                        
                        req_start = time.time()
                        await ws.send(json.dumps(message))
                        await ws.recv()
                        times.append(time.time() - req_start)
                        successful += 1
                        
        except Exception as e:
            failed += 1
            print(f"Worker {worker_id} failed: {e}")
    
    print(f"快速测试: {concurrency}并发, {duration}秒")
    tasks = [worker(i) for i in range(concurrency)]
    await asyncio.gather(*tasks, return_exceptions=True)
    
    if times:
        avg_time = sum(times) / len(times)
        qps = successful / duration
        print(f"QPS: {qps:.2f}, 平均响应: {avg_time:.3f}s, 成功: {successful}, 失败: {failed}")
    else:
        print("没有成功的请求")

if __name__ == "__main__":
    asyncio.run(quick_test())