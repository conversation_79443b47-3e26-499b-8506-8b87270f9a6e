# main.py
# from fastapi import FastAPI
from fastapi import FastAPI, Query, Request, File, UploadFile, Form
from io import BytesIO
import numpy as np
# from scipy.io import wavfile
import wave
import os,re
import tomli
from omegaconf import OmegaConf
from hydra.utils import get_class
from importlib.resources import files
import soundfile as sf

from datetime import datetime
from unidecode import unidecode
from pathlib import Path
import time
from fastapi.responses import Response, FileResponse
import uvicorn
import webbrowser
import torch

from f5_tts.infer.utils_infer import (
    cfg_strength,
    cross_fade_duration,
    device,
    fix_duration,
    infer_process,
    load_model,
    load_vocoder,
    mel_spec_type,
    nfe_step,
    preprocess_ref_audio_text,
    remove_silence_for_generated_wav,
    speed,
    sway_sampling_coef,
    target_rms,
)

def print_gpu_memory():
    if torch.cuda.is_available():
        current_device = torch.cuda.current_device()
        print(f"Device: {torch.cuda.get_device_name(current_device)}")
        print(f"Memory Allocated: {torch.cuda.memory_allocated() / 1024**2:.2f} MB")
        print(f"Max Memory Allocated: {torch.cuda.max_memory_allocated() / 1024**2:.2f} MB")
        print(f"Memory Reserved: {torch.cuda.memory_reserved() / 1024**2:.2f} MB")
    else:
        print("CUDA is not available.")

def preprocess_numbers(text):
    """将文本中的阿拉伯数字转换为中文数字
    Args:
        text: 包含数字的原始文本
    
    功能特点:
    1. 支持到亿级别的数字转换
    2. 处理特殊情况如"10"转"十"
    3. 正确处理零的显示规则
    4. 支持万、亿等单位的正确添加
    
    Returns:
        str: 数字已转换为中文的文本
    """
    number_map = {
            '0': '零', '1': '一', '2': '二', '3': '三', '4': '四',
            '5': '五', '6': '六', '7': '七', '8': '八', '9': '九'
        }
    def convert_digit_section(digit_str):
        """转换四位以内的数字段"""
        # number_map = {
        #     '0': '零', '1': '一', '2': '二', '3': '三', '4': '四',
        #     '5': '五', '6': '六', '7': '七', '8': '八', '9': '九'
        # }
        unit_map = ['', '十', '百', '千']
        
        result = ''
        digit_str = digit_str.zfill(4)  # 补零到4位
        has_previous_digit = False  # 用于处理零的显示
        
        for i, d in enumerate(digit_str):
            if d == '0':
                if has_previous_digit:  # 只有在前面有非零数字时才显示零
                    if result[-1] != '零':  # 避免重复的零
                        result += '零'
                has_previous_digit = False
            else:
                result += number_map[d] + unit_map[3-i]
                # result += number_map[d]
                has_previous_digit = True
        
        # 清理结果
        result = result.rstrip('零')  # 移除末尾的零
        
        # 处理特殊情况
        if result.startswith('一十'):
            result = result[1:]
            
        return result or '零'

    def convert_number(match):
        """转换匹配到的完整数字"""
        num = match.group(1)
        word = match.group(2)
        # print(f'{num} and {word}')
        
        # 如果后面有年字，只读数字，不处理成百千万
        if word == '年':
            result = ''
            for i, d in enumerate(num):
                result += number_map[d]
            result += '年'
            return result
        
        # 处理小于100的数字的特殊情况
        if len(num) <= 2:
            if num == '10':
                return '十'
            elif num.startswith('0'):
                return convert_digit_section(num.lstrip('0'))
            elif len(num) == 2 and num.startswith('1'):
                return '十' + (convert_digit_section(num[1]) if num[1] != '0' else '')
        
        # 对于大数字，按4位分段处理
        units = ['', '万', '亿']
        segments = []
        while num:
            seg = num[-4:] if len(num) >= 4 else num
            converted = convert_digit_section(seg)
            if converted != '零':  # 只添加非零的段
                segments.append(converted)
            num = num[:-4]
        
        # 添加单位并反转
        result = ''
        for i, seg in enumerate(segments):
            if seg != '零':  # 只给非零段加单位
                result = seg + units[i] + result
        if word:
            result += word
        return result or '零'
   
    return re.sub(r'(\d+)(?:([\u4e00-\u9fa5]))?', lambda m: convert_number(m), text)
    #     result += word
    #     return result or '零'
   
    # return re.sub(r'(\d+)([\u4e00-\u9fa5])', convert_number, text)

# 初始化 FastAPI
app = FastAPI()

async def tts_infer(text):
    text_process = preprocess_numbers(text)
    gen_text = config.get("gen_text", text_process)#
    save_chunk = config.get("save_chunk", False)
    use_legacy_text = config.get("no_legacy_text", False)  # no_legacy_text is a store_false arg
    if save_chunk and use_legacy_text:
        print(
            "\nWarning to --save_chunk: lossy ASCII transliterations of unicode text for legacy (.wav) file names, --no_legacy_text to disable.\n"
        )

    remove_silence = config.get("remove_silence", False)
    
    main_voice = {"ref_audio": ref_audio, "ref_text": ref_text}
    if "voices" not in config:
        voices = {"main": main_voice}
    else:
        voices = config["voices"]
        voices["main"] = main_voice
    for voice in voices:
        # print("Voice:", voice)
        # print("ref_audio ", voices[voice]["ref_audio"])
        voices[voice]["ref_audio"], voices[voice]["ref_text"] = preprocess_ref_audio_text(
            voices[voice]["ref_audio"], voices[voice]["ref_text"]
        )
        # print("ref_audio_", voices[voice]["ref_audio"], "\n\n")

    generated_audio_segments = []
    reg1 = r"(?=\[\w+\])"
    chunks = re.split(reg1, gen_text)
    reg2 = r"\[(\w+)\]"

  
    s_time = time.time()
    for text in chunks:
        if not text.strip():
            continue
        match = re.match(reg2, text)
        if match:
            voice = match[1]
        else:
            # print("No voice tag found, using main.")
            voice = "main"
        if voice not in voices:
            print(f"Voice {voice} not found, using main.")
            voice = "main"
        text = re.sub(reg2, "", text)
        ref_audio_ = voices[voice]["ref_audio"]
        ref_text_ = voices[voice]["ref_text"]
        local_speed = voices[voice].get("speed", speed)
        gen_text_ = text.strip()
        # print(f"Voice: {voice}")
        audio_segment, final_sample_rate, spectrogram = infer_process(
            ref_audio_,
            ref_text_,
            gen_text_,
            ema_model,
            vocoder,
            mel_spec_type=vocoder_name,
            target_rms=target_rms,
            cross_fade_duration=cross_fade_duration,
            nfe_step=nfe_step,
            cfg_strength=cfg_strength,
            sway_sampling_coef=sway_sampling_coef,
            speed=local_speed,
            fix_duration=fix_duration,
            device=device,
        )
        # print_gpu_memory()
        generated_audio_segments.append(audio_segment)

        e_time = time.time()
        print(f'time cost {e_time-s_time}')

    if generated_audio_segments:
        final_wave = np.concatenate(generated_audio_segments)

    # with BytesIO() as wavContent:
    #     with wave.open(wavContent,'wb') as wf:
    #         wf.setnchannels(1)
    #         wf.setsampwidth(2)
    #         wf.setframerate(final_sample_rate)

    #         audio_data = (final_wave*32767).astype(np.int16).tobytes()
    #         wf.writeframes(audio_data)
    #     response = Response(content=wavContent.getvalue(), media_type="audio/wav")
    #     return response
    # 转换为目标格式（例如 MP3）
    save_format = config.get("save_format", "aac")
    converted_audio = convert_audio_format(final_wave, final_sample_rate, format=save_format)

    # 返回响应
    response = Response(content=converted_audio, media_type=f"audio/{save_format}")
    return response
    
from pydub import AudioSegment

def convert_audio_format(audio_data: np.ndarray, sample_rate: int, format: str = "mp3") -> bytes:
    """
    将音频数据从 WAV 格式转换为指定格式（如 mp3、m4a、aac）

    Args:
        audio_data (np.ndarray): 音频数据数组
        sample_rate (int): 采样率
        format (str): 目标格式，默认为 "mp3"

    Returns:
        bytes: 转换后的音频数据
    """
    # 将 NumPy 数组转换为 PyDub 的 AudioSegment 对象
    audio_segment = AudioSegment(
        data=(audio_data * 32767).astype(np.int16).tobytes(),
        frame_rate=sample_rate,
        sample_width=2,
        channels=1
    )

    # 将音频转换为目标格式
    converted_audio = BytesIO()
    if format == 'mp3':
        audio_segment.export(converted_audio, format=format, bitrate="128k")
    elif format == 'm4a':
        audio_segment.export(converted_audio, format=format, bitrate="96k")
    elif format == 'aac':
        audio_segment.export(converted_audio, format='adts', bitrate="64k")
    else:
        audio_segment.export(converted_audio, format=format)
    return converted_audio.getvalue()

@app.post("/tts")
async def tts_predict(text: str = Form(...),):
    return await tts_infer(text)

import sys
if __name__ == "__main__":
    # 输出当前文件夹
    print(os.getcwd())
    model_cfg_path = 'f5_tts/infer/examples/basic/basic.toml'
    config = tomli.load(open(model_cfg_path, "rb"))

    model_cfg = 'f5_tts/configs/E2TTS_Base.yaml'
    model = "F5TTS_v1_Base"
    model_cfg = OmegaConf.load(config.get("model_cfg", str(files("f5_tts").joinpath(f"configs/{model}.yaml")))
    )

    model_cls = get_class(f"f5_tts.model.{model_cfg.model.backbone}")
    model_arc = model_cfg.model.arch
    vocab_file = config.get("vocab_file", "")

    repo_name, ckpt_step, ckpt_type = "F5-TTS", 1250000, "safetensors"

    ckpt_file = f"../ckpts/SWivid/{repo_name}/{model}/model_{ckpt_step}.{ckpt_type}"

    vocoder_name = "vocos"
    if vocoder_name == "vocos":
        vocoder_local_path = "../checkpoints/charactr/vocos-mel-24khz"
    elif vocoder_name == "bigvgan":
        vocoder_local_path = "../checkpoints/charactr/bigvgan_v2_24khz_100band_256x"

    vocoder = load_vocoder(
        vocoder_name=vocoder_name, is_local=True, local_path=vocoder_local_path, device=device
    )

    print(f"Using {model}...")
    ema_model = load_model(
        model_cls, model_arc, ckpt_file, mel_spec_type=vocoder_name, vocab_file=vocab_file, device=device
    )


    speaker_dict = {
        "yyz": "各位来宾，大家好，现在我们已经进入了红军长征湘江战役纪念园的区域，这个纪念园建在湘江战役脚山铺阻击战的战场遗址上。",
        "jinyidan": "这儿歌你熟悉吗？这儿歌啊，可以说是全民儿歌，当年呢奶奶教会了我们，现在呢我们。",
        #"country": "f5_tts/infer/examples/basic/yyz_promot.wav",
    }
    speaker = 'yyz'
    ref_audio = config.get("ref_audio", f"f5_tts/infer/examples/basic/{speaker}_promot.wav") #basic_ref_en.wav jinyidan_promot.wav
    ref_text = config.get("ref_text", speaker_dict[speaker]) #"Some call me nature, others call me mother nature.") #'这儿歌你熟悉吗？这儿歌啊，可以说是全民儿歌，当年呢奶奶教会了我们，现在呢我们。')#

    port = f'{8082+int(sys.argv[1])}'
    uvicorn.run( app, port=int(port), host="127.0.0.1", log_level="info")