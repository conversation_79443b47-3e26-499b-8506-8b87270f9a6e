import asyncio
import websockets
import json
import time
import statistics
from datetime import datetime
import base64
import os
import random

# 配置项
WS_URL = "ws://127.0.0.1:9214/chat"
IMAGE_PATH = "/private/huqq/Projects/Navigation/location_mast3r_slam/logs/img87d/"

# 预加载音频文件
with open('/private/liulz/TTS/F5-TTS/src/tests/infer_cli_20250718_152229.wav', 'rb') as f:
    audio_base64 = base64.b64encode(f.read()).decode()

async def benchmark_concurrency(url, image_path, concurrency_level, duration=60):
    """测试指定并发级别下的WebSocket性能"""
    successful_requests = 0
    failed_requests = 0
    connection_failures = 0
    request_times = []
    error_details = {}
    
    # 预加载图片列表
    img_list = os.listdir(image_path)  # 只用前2张图片
    selected_images = random.sample(img_list, 2)
    async def worker(worker_id):
        nonlocal successful_requests, failed_requests, connection_failures
        
        try:
            async with websockets.connect(url) as websocket:
                start_time = time.time()
                
                while time.time() - start_time < duration:
                    for img_name in selected_images:
                        if time.time() - start_time >= duration:
                            break
                            
                        full_path = os.path.join(image_path, img_name)
                        
                        # 构建消息
                        message = {
                            'id': f"worker_{worker_id}_{img_name}_{int(time.time()*1000)}",
                            'msg': "query",
                            'audio': audio_base64,
                            'video': "",
                            'exhibition_id': 0,
                            'frame_time': round(time.time(), 2)
                        }
                        
                        try:
                            with open(full_path, 'rb') as f:
                                message['video'] = base64.b64encode(f.read()).decode()
                        except Exception as e:
                            print(f"Worker {worker_id}: 加载图片失败: {e}")
                            continue
                        
                        # 发送请求并计时
                        req_start = time.time()
                        try:
                            await websocket.send(json.dumps(message))
                            response = await websocket.recv()
                            req_time = time.time() - req_start
                            
                            successful_requests += 1
                            request_times.append(req_time)
                            
                        except websockets.exceptions.ConnectionClosed:
                            failed_requests += 1
                            error_details["ConnectionClosed"] = error_details.get("ConnectionClosed", 0) + 1
                            break
                        except Exception as e:
                            failed_requests += 1
                            error_type = type(e).__name__
                            error_details[error_type] = error_details.get(error_type, 0) + 1
                            
        except Exception as e:
            connection_failures += 1
            error_type = f"Connection_{type(e).__name__}"
            error_details[error_type] = error_details.get(error_type, 0) + 1
            print(f"Worker {worker_id}: 连接失败: {e}")
    
    # 启动所有worker
    tasks = [worker(i) for i in range(concurrency_level)]
    await asyncio.gather(*tasks, return_exceptions=True)
    
    total_requests = successful_requests + failed_requests
    qps = successful_requests / duration if duration > 0 else 0
    
    # 计算响应时间统计
    if request_times:
        avg_time = statistics.mean(request_times)
        median_time = statistics.median(request_times)
        min_time = min(request_times)
        max_time = max(request_times)
        p95_time = statistics.quantiles(request_times, n=20)[18] if len(request_times) >= 20 else max_time
        p99_time = statistics.quantiles(request_times, n=100)[98] if len(request_times) >= 100 else max_time
    else:
        avg_time = median_time = min_time = max_time = p95_time = p99_time = 0
    
    return {
        'concurrency': concurrency_level,
        'duration': duration,
        'total_requests': total_requests,
        'successful_requests': successful_requests,
        'failed_requests': failed_requests,
        'connection_failures': connection_failures,
        'success_rate': (successful_requests/total_requests*100) if total_requests > 0 else 0,
        'qps': qps,
        'avg_response_time': avg_time,
        'median_response_time': median_time,
        'min_response_time': min_time,
        'max_response_time': max_time,
        'p95_response_time': p95_time,
        'p99_response_time': p99_time,
        'error_details': error_details
    }

async def test_single_connection_stability(url, image_path, duration=300):
    """测试单连接长时间稳定性"""
    print(f"测试单连接稳定性 ({duration}秒)...")
    
    successful_requests = 0
    failed_requests = 0
    request_times = []
    
    img_list = os.listdir(image_path)[:2]
    
    try:
        async with websockets.connect(url) as websocket:
            start_time = time.time()
            
            while time.time() - start_time < duration:
                for img_name in img_list:
                    if time.time() - start_time >= duration:
                        break
                        
                    full_path = os.path.join(image_path, img_name)
                    message = {
                        'id': f"stability_test_{int(time.time()*1000)}",
                        'msg': "query",
                        'audio': audio_base64,
                        'video': "",
                        'exhibition_id': 0,
                        'frame_time': round(time.time(), 2)
                    }
                    
                    try:
                        with open(full_path, 'rb') as f:
                            message['video'] = base64.b64encode(f.read()).decode()
                    except:
                        continue
                    
                    req_start = time.time()
                    try:
                        await websocket.send(json.dumps(message))
                        await websocket.recv()
                        req_time = time.time() - req_start
                        
                        successful_requests += 1
                        request_times.append(req_time)
                        
                    except Exception as e:
                        failed_requests += 1
                        print(f"请求失败: {e}")
                    
                    await asyncio.sleep(0.1)  # 避免过于频繁
                    
    except Exception as e:
        print(f"连接失败: {e}")
    
    print(f"稳定性测试结果: 成功{successful_requests}, 失败{failed_requests}")
    if request_times:
        print(f"平均响应时间: {statistics.mean(request_times):.3f}s")

async def main():
    print("开始WebSocket接口压力测试...")
    print("="*80)
    print(f"目标URL: {WS_URL}")
    print(f"图片路径: {IMAGE_PATH}")
    
    # 先测试单连接稳定性
    await test_single_connection_stability(WS_URL, IMAGE_PATH, duration=60)
    print("\n" + "="*80)
    
    # 测试不同并发级别
    concurrency_levels = [1, 5, 10, 20, 50]#, 100
    results = []
    
    for level in concurrency_levels:
        print(f"\n测试并发级别: {level}")
        print("-" * 40)
        
        result = await benchmark_concurrency(WS_URL, IMAGE_PATH, level, duration=60)
        results.append(result)
        
        print(f"QPS: {result['qps']:.2f}")
        print(f"成功率: {result['success_rate']:.1f}%")
        print(f"连接失败: {result['connection_failures']}")
        print(f"平均响应时间: {result['avg_response_time']:.3f}s")
        print(f"95%响应时间: {result['p95_response_time']:.3f}s")
        
        if result['error_details']:
            print(f"错误详情: {result['error_details']}")
        
        # 如果成功率太低或连接失败太多，停止测试
        if result['success_rate'] < 70 or result['connection_failures'] > level * 0.5:
            print("性能下降明显，停止测试更高并发")
            break
            
        # 间隔时间让服务器恢复
        await asyncio.sleep(5)
    
    # 输出汇总报告
    print("\n" + "="*100)
    print("WebSocket接口压力测试汇总报告")
    print("="*100)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 100)
    print(f"{'并发数':<8} {'QPS':<10} {'成功率':<8} {'连接失败':<10} {'平均响应':<10} {'P95响应':<10} {'错误数':<8}")
    print("-" * 100)
    
    for result in results:
        print(f"{result['concurrency']:<8} {result['qps']:<10.2f} "
              f"{result['success_rate']:<8.1f}% {result['connection_failures']:<10} "
              f"{result['avg_response_time']:<10.3f} {result['p95_response_time']:<10.3f} "
              f"{result['failed_requests']:<8}")
    
    # 性能分析
    if results:
        best_qps_result = max(results, key=lambda x: x['qps'])
        print(f"\n最佳QPS: {best_qps_result['qps']:.2f} (并发数: {best_qps_result['concurrency']})")
        
        stable_results = [r for r in results if r['success_rate'] > 95 and r['connection_failures'] == 0]
        if stable_results:
            best_stable = max(stable_results, key=lambda x: x['qps'])
            print(f"最佳稳定并发: {best_stable['concurrency']} (QPS: {best_stable['qps']:.2f})")

if __name__ == "__main__":
    asyncio.run(main())