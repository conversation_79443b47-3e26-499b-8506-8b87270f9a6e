import aiohttp
import asyncio
import time
import statistics
from datetime import datetime

# 配置项
# URL = "http://f5ttsserver/tts"
URL = "http://127.0.0.1:8080/f5tts/tts"
DATA = {'text': '大家好，人民网成立25周年，25年成立。'} #hello world! 
TOTAL_REQUESTS = 3

# 全局统计变量
successful_requests = 0
failed_requests = 0
request_times = []
lock = asyncio.Lock()

async def send_request(session, request_id):
    """发送单个异步请求"""
    global successful_requests, failed_requests
    
    start_time = time.time()
    try:
        async with session.post(URL, data=DATA) as response:
            await response.read()
            elapsed_time = time.time() - start_time
            # print(response.status)
            async with lock:
                if response.status == 200:
                    successful_requests += 1
                    request_times.append(elapsed_time)
                else:
                    failed_requests += 1
                    
            return request_id, response.status, elapsed_time
            
    except Exception as e:
        elapsed_time = time.time() - start_time
        async with lock:
            failed_requests += 1
        print(f"Request {request_id} failed: {str(e)}")
        return request_id, 0, elapsed_time

async def main():
    print(f"开始并发测试: {TOTAL_REQUESTS} 个并发请求")
    print(f"目标URL: {URL}")
    print("-" * 50)
    
    # 设置连接池参数以支持高并发
    connector = aiohttp.TCPConnector(
        limit=200,  # 总连接池大小
        limit_per_host=100,  # 每个主机的连接数
        ttl_dns_cache=300,
        use_dns_cache=True,
    )
    
    timeout = aiohttp.ClientTimeout(total=30)  # 30秒超时
    
    async with aiohttp.ClientSession(
        connector=connector, 
        timeout=timeout
    ) as session:
        tasks = [send_request(session, i) for i in range(TOTAL_REQUESTS)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        return results

if __name__ == "__main__":
    start_time = time.time()
    results = asyncio.run(main())
    end_time = time.time()
    
    total_time = end_time - start_time
    
    # 计算详细统计信息
    if request_times:
        avg_time = statistics.mean(request_times)
        median_time = statistics.median(request_times)
        min_time = min(request_times)
        max_time = max(request_times)
        p95_time = statistics.quantiles(request_times, n=20)[18] if len(request_times) >= 20 else max_time
        p99_time = statistics.quantiles(request_times, n=100)[98] if len(request_times) >= 100 else max_time
    else:
        avg_time = median_time = min_time = max_time = p95_time = p99_time = 0
    
    # 输出性能报告
    print("\n" + "="*60)
    print("FastAPI 并发性能测试报告")
    print("="*60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"并发请求数: {TOTAL_REQUESTS}")
    print(f"总耗时: {total_time:.2f}秒")
    print(f"成功请求数: {successful_requests}")
    print(f"失败请求数: {failed_requests}")
    print(f"成功率: {(successful_requests/(successful_requests+failed_requests)*100):.1f}%")
    print(f"QPS (每秒请求数): {successful_requests/total_time:.2f}")
    print(f"平均并发度: {TOTAL_REQUESTS/total_time:.2f}")
    print("-" * 40)
    print("响应时间统计:")
    print(f"  平均响应时间: {avg_time:.3f}秒")
    print(f"  中位数响应时间: {median_time:.3f}秒")
    print(f"  最小响应时间: {min_time:.3f}秒")
    print(f"  最大响应时间: {max_time:.3f}秒")
    print(f"  95%响应时间: {p95_time:.3f}秒")
    print(f"  99%响应时间: {p99_time:.3f}秒")
    print("="*60)
