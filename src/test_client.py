import requests
import os
import time
url = "http://127.0.0.1:14082/tts" # "http://127.0.0.1:8080/f5tts/tts"  #

# 16000：2.8 24000：3.23
text = """2025年11月，2025年12月，2023年11月，各位来宾，大家好，我是讲解员AI之眼，2025年七月3日，nice，很高兴为大家讲解。现在我们已经进入了红军长征湘江战役纪念园的区域，这个纪念园建在湘江战役脚山铺阻击战的战场遗址上。我们行驶的这条公路是国道桂黄公路，它的左边是先锋岭，右边是米花山，先锋岭和米花山都是湘江战役的主战场，湘江就位于我们正前方3公里的地方。大家看路边这一排排青翠的刚竹，它们象征着红军战士刚强坚韧、百折不挠的品质。"""
# text = "大家好，人民网成立25周年，25年成立。" #16000：0.69 24000：0.69
# text = "你好，我是AI之眼，你可以叫我小青小青" # 16000：0.68 24000：0.68
format = 'mp3' #'mp3' #'aac' 'm4a' 'wav'
data = {
    "text":text,
    "format":format
}

count = 0
AVG_TIME = 0
while True:
    s_time = time.time()
    response = requests.post(url, data=data)
    if response.status_code == 200:
        #判断返回数据的类型，audio/wav 或者 json
        content_type = response.headers.get("Content-Type")
        if content_type == f"audio/{format}":
            audio_data = response.content
            # Process the audio data as needed
            print("Audio data received. Length:", len(audio_data))
            # Save audio data to a file
            if audio_data:
                file_path = f"audio.{format}"  # Replace with the desired file path
                with open(file_path, "wb") as file:
                    file.write(audio_data)
                    print("Audio data saved to:", file_path)
            else:
                print("No audio data found in the result.")
        else:
            result = response.json()
            print("Response:", result)
    else:
        print("Request failed with status code:", response.status_code)
    e_time = time.time()
    print(f'time cost {e_time-s_time}')
    AVG_TIME = (AVG_TIME*count + (e_time-s_time))/(count+1)
    count += 1
    if count >= 1:
        break
print(f'AVG_TIME: {AVG_TIME}')


